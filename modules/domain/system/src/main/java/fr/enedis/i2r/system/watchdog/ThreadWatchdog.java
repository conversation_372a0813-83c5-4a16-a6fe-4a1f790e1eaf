package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ThreadWatchdog {

    private final Logger logger = LoggerFactory.getLogger(ThreadWatchdog.class);

    private final ConcurrentMap<String, Instant> threads = new ConcurrentHashMap<>();
    private final ConcurrentMap<String, ScheduledFuture<?>> scheduledHeartbeats = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler;
    private Optional<ScheduledFuture<?>> timeoutChecker = Optional.empty();

    private Duration heartbeatInterval = Duration.ofSeconds(45);
    private Duration threadTimeout = heartbeatInterval.multipliedBy(2);

    public ThreadWatchdog() {
        this(Executors.newScheduledThreadPool(5));
    }

    public ThreadWatchdog(ScheduledExecutorService scheduler) {
        this.scheduler = scheduler;
    }

    public void setHeartbeatInterval(Duration interval) {
        this.heartbeatInterval = interval;
        this.threadTimeout = heartbeatInterval.multipliedBy(2);
    }

    public void register(String threadName) {
        threads.put(threadName, Instant.now());
        startHeartbeat(threadName);
        startTimeoutChecker();
        logger.info("Thread registered for monitoring: {}", threadName);
    }

    private void startTimeoutChecker() {
        if (timeoutChecker.isEmpty() || timeoutChecker.get().isCancelled()) {
            ScheduledFuture<?> checker = scheduler.scheduleAtFixedRate(
                this::checkForDeadThreads,
                threadTimeout.toMillis(),
                heartbeatInterval.toMillis(),
                TimeUnit.MILLISECONDS
            );
            timeoutChecker = Optional.of(checker);
            logger.info("Thread timeout checker started with {}ms timeout, checking every {}ms",
                threadTimeout.toMillis(), heartbeatInterval.toMillis());
        }
    }

    private void checkForDeadThreads() {
        Instant now = Instant.now();
        threads.entrySet().removeIf(entry -> {
            String threadName = entry.getKey();
            Instant lastHeartbeat = entry.getValue();
            Duration timeSinceLastHeartbeat = Duration.between(lastHeartbeat, now);

            if (timeSinceLastHeartbeat.compareTo(threadTimeout) > 0) {
                logger.error("CRITICAL - DEAD THREAD DETECTED: {} - Last heartbeat was {}s ago (timeout: {}s) - "
                    + "Triggering System.exit(1) for systemd restart",
                    threadName, timeSinceLastHeartbeat.toSeconds(), threadTimeout.toSeconds());

                // Cancel the heartbeat scheduler for this dead thread
                ScheduledFuture<?> future = scheduledHeartbeats.remove(threadName);
                if (future != null) {
                    future.cancel(false);
                    logger.info("Heartbeat scheduler cancelled for dead thread: {}", threadName);
                }

                try {
                    Thread.sleep(1000); // Give logs time to flush
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                System.exit(1);

                return true; // Remove from threads map
            }
            return false; // Keep in threads map
        });
    }

    private void startHeartbeat(String threadName) {
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
            () -> heartbeat(threadName),
            heartbeatInterval.toMillis(),
            heartbeatInterval.toMillis(),
            TimeUnit.MILLISECONDS
        );
        scheduledHeartbeats.put(threadName, future);
        logger.info("Heartbeat started for thread: {} with interval: {}ms", threadName, heartbeatInterval.toMillis());
    }

    private void heartbeat(String threadName) {
        Instant lastHeartbeat = threads.get(threadName);
        if (lastHeartbeat != null) {
            threads.put(threadName, Instant.now());
            logger.debug("Heartbeat recorded for thread: {} at {}", threadName, Instant.now());
        } else {
            logger.warn("Heartbeat for unregistered thread: {}", threadName);
        }
    }

    public void unregister(String threadName) {
        Instant removed = threads.remove(threadName);
        ScheduledFuture<?> future = scheduledHeartbeats.remove(threadName);
        if (future != null) {
            future.cancel(false);
            logger.info("Heartbeat stopped for thread: {}", threadName);
        }
        if (removed != null) {
            logger.info("Thread unregistered from monitoring: {}", threadName);
        }
    }

    public void shutdown() {
        threads.clear();
        scheduledHeartbeats.values().forEach(future -> future.cancel(false));
        scheduledHeartbeats.clear();

        // Stop timeout checker if running
        timeoutChecker.ifPresent(future -> {
            if (!future.isCancelled()) {
                future.cancel(false);
                logger.debug("Timeout checker stopped");
            }
        });
        timeoutChecker = Optional.empty();

        logger.debug("All threads cleared from monitoring");
        scheduler.shutdown();
        logger.info("ThreadWatchdog scheduler shutdown");
    }

    public ConcurrentMap<String, Instant> getThreads() {
        return threads;
    }
}
